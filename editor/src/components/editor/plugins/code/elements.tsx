import useEditor from '../../use-editor';
import { ChangeEvent } from 'react';
import Modal from '../../../modal';
import { findPath } from '../../queries';
import { setNodes } from '../../transforms';
import { styled } from '@topwrite/common';
import { ElementProps } from '../../plate-plugin';
import { CodeElement } from 'slate';

export const Code = ({ element, children, attributes, ElementMenu }: ElementProps<CodeElement>) => {
    const editor = useEditor();
    const handleChange = async (e: ChangeEvent<HTMLSelectElement>) => {
        let lang;
        if (e.target.value === '__other__') {
            const result = await Modal.prompt<{ lang: string }>({
                title: 'Choose a highlighting language',
                schema: {
                    properties: {
                        lang: {
                            type: 'string',
                            title: 'Language identifier',
                            description: 'Enable syntax highlighting by providing the identifier for your code block\'s language. For example ruby, cpp, markdown, haskell, etc.'
                        }
                    },
                    required: ['lang'],
                }
            });
            if (result) {
                lang = result.lang;
            } else {
                return;
            }
        } else {
            lang = e.target.value;
        }

        const location = findPath(editor, element);
        setNodes(editor, { lang }, { at: location });
    };

    const langs = {
        js: 'JavaScript',
        py: 'Python',
        cpp: 'C++',
        c: 'C',
        php: 'PHP',
        java: 'Java',
        rust: 'Rust',
        bash: 'Bash',
        css: 'CSS',
        markdown: 'Markdown',
        asciidoc: 'Asciidoc',
        ruby: 'Ruby',
    };

    const lang = element.lang || '';

    return <ElementMenu element={element} {...attributes} as={Container}>
        <Content>{children}</Content>
        <Select contentEditable={false}>
            <select onChange={handleChange} value={lang}>
                <option value=''>Auto</option>
                <option disabled>-------</option>
                {Object.entries(langs)
                       .map(([lang, title]) => <option key={lang} value={lang}>{title}</option>)}
                <option disabled>-------</option>
                <option value='__other__'>Other</option>
                {lang && !Object.keys(langs).includes(lang) && <option value={lang}>{lang}</option>}
            </select>
        </Select>
    </ElementMenu>;
};


const Select = styled.div`
    position: absolute;
    top: 0;
    right: 0;
    user-select: none;

    select {
        appearance: none;
        background: var(--ttw-border-color);
        color: var(--ttw-box-color);
        padding: 2px 7px;
        font-size: 12px !important;
        cursor: pointer;
        border-radius: 0 4px 0 4px;
        border: none;
        outline: 0;
        display: block;

        option {
            background: var(--ttw-box-background);
        }
    }
`;

const Content = styled.div`
    font-family: Consolas, "Liberation Mono", Menlo, Courier, monospace;
    overflow: auto;
    word-wrap: normal;
    padding: .85em 1em;
    background: var(--ttw-foreground);
    border-radius: 4px;
    white-space: pre;
    margin: 1em 0;
`;

const Container = styled.div`
    position: relative;
`;
