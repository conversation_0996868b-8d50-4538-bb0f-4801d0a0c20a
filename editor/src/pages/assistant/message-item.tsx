import { css, Markdown, styled } from '@topwrite/common';
import { Fragment } from 'react';
import { ReactComponent as LoadingIcon } from '../../images/three-dots.svg';
import Content, { ContentType } from './content';
import Tool, { ToolMessage } from './tool';

export interface Message {
    id?: string;
    input?: {
        query: string
        context?: {
            filename: string;
            range?: FileRange | null;
        }
    };
    output?: {
        content: string | ContentType[];
        tools?: ToolMessage[]
        error?: string
    }[];
    loading?: boolean;
}

interface Props {
    message: Message;
}

export default function MessageItem({ message }: Props) {
    const { input, output, loading } = message;

    return <>
        {input && <Container $reverse={true}>
            <Body>
                <Markdown>{input.query}</Markdown>
            </Body>
        </Container>}
        {output && <Container>
            <Body>
                <Chunks>
                    {output.length > 0 && output.map((chunk, index) => {
                        return <Fragment key={index}>
                            {chunk.content && <Content value={chunk.content} />}
                            {(chunk.tools || []).map((tool, index) => {
                                return <Tool key={index} tool={tool} loading={loading} />;
                            })}
                            {chunk.error && <Markdown>{`[${chunk.error}]`}</Markdown>}
                        </Fragment>;
                    })}
                </Chunks>
                {loading && <LoadingIcon />}
            </Body>
        </Container>}
    </>;
}


const Body = styled.div`
    padding: .875rem;
    background-color: rgb(243, 243, 243);
    border-radius: var(--bs-border-radius-lg);
    max-width: 100%;
    position: relative;
`;

const Container = styled.div<{ $reverse?: boolean }>`
    display: flex;
    justify-content: flex-start;
    margin-bottom: 1rem;

    &:last-child {
        margin-bottom: 0;
    }

    ${props => props.$reverse && css`
        flex-direction: row-reverse;

        ${Body} {
            background: rgba(var(--bs-primary-rgb), 0.1);
        }
    `};
`;

const Chunks = styled.div`

`;
