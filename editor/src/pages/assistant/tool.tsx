import { Fragment, useState } from 'react';
import { Spinner } from 'react-bootstrap';
import Content, { ContentType } from './content';
import { BsXCircleFill, BsCheckCircleFill, BsCaretUpFill, BsCaretDownFill, BsDashCircleFill } from 'react-icons/bs';
import { styled } from '@topwrite/common';

export interface ToolMessage {
    name: string;
    title: string;
    arguments: string;
    response?: string;
    error?: boolean;
    content?: ContentType;
}

interface Props {
    tool: ToolMessage;
    loading?: boolean;
}

const formatArguments = (tool: ToolMessage) => {
    try {
        return JSON.stringify(JSON.parse(tool.arguments), null, 4);
    } catch (e) {
        return tool.arguments;
    }
};

export default function Tool({ tool, loading }: Props) {
    const [show, setShow] = useState(false);
    const hasResponse = 'response' in tool;
    const isCancelled = !hasResponse && !loading;

    return <Fragment>
        <div className='mb-2'>
            {show && (hasResponse || isCancelled) ? <div className='shadow-sm rounded bg-white fs-7'>
                    <div onClick={() => setShow(false)} role='button' className='d-flex align-items-center p-1 px-2 gap-2'>
                        {isCancelled ? <BsDashCircleFill className='text-muted' /> :
                            (tool.error ? <BsXCircleFill className='text-danger' /> :
                                <BsCheckCircleFill className='text-success' />)}
                        <span className='text-muted'>{isCancelled ? '已取消' : '已使用'}</span>
                        <span>{tool.title}</span>
                        <BsCaretUpFill className='text-muted' />
                    </div>
                    <div className='border-top p-2 d-flex flex-column gap-2'>
                        <div className='border rounded bg-light'>
                            <div className='d-flex align-items-center p-1 px-2 fs-7 gap-2'>
                                <span className='text-muted'>参数</span>
                            </div>
                            <Response className='border-top p-2'>
                                {formatArguments(tool)}
                            </Response>
                        </div>
                        {!isCancelled && <div className='border rounded bg-light'>
                            <div className='d-flex align-items-center p-1 px-2 fs-7 gap-2'>
                                <span className='text-muted'>响应</span>
                            </div>
                            <Response className='border-top p-2'>
                                {tool.response || 'None'}
                            </Response>
                        </div>}
                    </div>
                </div> :
                <div onClick={() => setShow(hasResponse || isCancelled)} role='button' className='d-inline-flex align-items-center shadow-sm rounded bg-white p-1 px-2 fs-7 gap-2'>
                    {isCancelled ? <BsDashCircleFill className='text-muted' /> :
                        (hasResponse ? (tool.error ? <BsXCircleFill className='text-danger' /> :
                                <BsCheckCircleFill className='text-success' />) :
                            <Spinner animation='border' variant='primary' size='sm' />)}
                    <span className='text-muted'>{isCancelled ? '已取消' : (hasResponse ? '已使用' : '正在使用')}</span>
                    <span>{tool.title}</span>
                    {(hasResponse || isCancelled) && <BsCaretDownFill className='text-muted' />}
                </div>
            }
        </div>
        {tool.content && <Content value={tool.content} />}
    </Fragment>;
}

const Response = styled.div`
    white-space: pre-wrap;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 150px;
`;
