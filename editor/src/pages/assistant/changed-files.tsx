import { styled, useAsyncCallback, useModel, useSelector } from '@topwrite/common';
import { useCallback, useMemo, useState } from 'react';
import { Button, ButtonGroup, Dropdown } from 'react-bootstrap';
import { BsFileText, BsTrash } from 'react-icons/bs';
import { MdUnfoldLess, MdUnfoldMore } from 'react-icons/md';
import { generateCommitMessage } from '../../lib/generate-commit-message';
import { socket } from '../../lib/socket';
import useFormatMessage from '../../lib/use-format-message';
import confirm from '../../components/modal/confirm';
import Tooltip from '../../components/tooltip';
import DiffModal from './diff-modal';

export default function ChangedFiles() {
    const [collapsed, setCollapsed] = useState(true);
    const [selectedFile, setSelectedFile] = useState<string | null>(null);
    const [{ status: { changes }, release: releaseState, syncing }, { commit }] = useModel('workspace');
    const { release } = useSelector('options');
    const t = useFormatMessage();

    const fileCount = Object.keys(changes).length;
    const releasing = releaseState === 'pending' || releaseState === 'running';

    const { execute: discardAll, loading: discardingAll } = useAsyncCallback(() => socket.discard('.'));

    // 生成默认提交消息
    const defaultMessage = useMemo(() => generateCommitMessage(changes), [changes]);

    const handleDiscardAll = useCallback(async () => {
        if (await confirm({
            title: t('commit.reset_all_title'),
            message: t('commit.reset_all_message')
        })) {
            await discardAll();
        }
    }, [discardAll, t]);

    const handleDiscardFile = useCallback(async (filename: string, event: React.MouseEvent) => {
        event.stopPropagation(); // 阻止事件冒泡，避免触发文件点击事件
        if (await confirm({
            title: t('commit.reset_title', { filename }),
            message: t('commit.reset_message')
        })) {
            await socket.discard(filename);
        }
    }, [t]);

    const handleCommit = useCallback((withRelease: boolean = false) => {
        commit(defaultMessage, withRelease);
    }, [commit, defaultMessage]);

    return <Container>
        <Header onClick={() => setCollapsed(!collapsed)}>
            <HeaderLeft>
                <CollapseButton>
                    {collapsed ? <MdUnfoldLess /> : <MdUnfoldMore />}
                </CollapseButton>
                <span>{fileCount} {fileCount === 1 ? 'file' : 'files'} changed</span>
            </HeaderLeft>
        </Header>
        {!collapsed && <Content>
            {fileCount === 0 ? <EmptyState>{t('commit.empty')}</EmptyState> : <>
                <FileList>
                    {Object.entries(changes).map(([path, change], index) => (
                        <FileItem
                            key={index}
                            $status={change}
                            onClick={() => setSelectedFile(path)}
                        >
                            <FileContent>
                                <FileIcon $status={change}>
                                    <BsFileText />
                                </FileIcon>
                                <FileName>{path}</FileName>
                            </FileContent>
                            <FileActions>
                                <DiscardButton
                                    variant='light'
                                    size='sm'
                                    onClick={(e) => handleDiscardFile(path, e)}
                                    title='放弃此文件的变更'
                                >
                                    <BsTrash />
                                </DiscardButton>
                            </FileActions>
                        </FileItem>
                    ))}
                </FileList>
                <Actions>
                    <Button
                        variant='outline-secondary'
                        size='sm'
                        className={'w-50'}
                        onClick={handleDiscardAll}
                        disabled={discardingAll}
                    >
                        {discardingAll ? t('commit.reset_all') : t('commit.reset')}
                    </Button>
                    {release ? (
                        <Dropdown as={ButtonGroup} className={'w-50'}>
                            <Button
                                size={'sm'}
                                className={'w-100'}
                                variant='primary'
                                onClick={() => handleCommit(false)}
                                disabled={syncing || fileCount === 0}
                            >
                                {syncing ? t('commit.submit') + '...' : t('commit.submit')}
                            </Button>
                            <Dropdown.Toggle
                                split
                                variant='primary'
                                size={'sm'}
                                disabled={syncing || fileCount === 0}
                            />
                            <Dropdown.Menu>
                                {releasing ?
                                    <Tooltip wrap tooltip={t('commit.releasing')}>
                                        <Dropdown.Item disabled>
                                            {t('commit.submit_with_release')}
                                        </Dropdown.Item>
                                    </Tooltip> :
                                    <Dropdown.Item onClick={() => handleCommit(true)}>
                                        {t('commit.submit_with_release')}
                                    </Dropdown.Item>
                                }
                            </Dropdown.Menu>
                        </Dropdown>
                    ) : (
                        <Button
                            size={'sm'}
                            className={'w-50'}
                            variant='primary'
                            onClick={() => handleCommit(false)}
                            disabled={syncing || fileCount === 0}
                        >
                            {syncing ? t('commit.submit') + '...' : t('commit.submit')}
                        </Button>
                    )}
                </Actions>
            </>}
        </Content>}
        {selectedFile && <DiffModal
            filename={selectedFile}
            show={!!selectedFile}
            onHide={() => setSelectedFile(null)}
        />}
    </Container>;
}

const Container = styled.div`
    background: var(--ttw-box-background);
    color: var(--ttw-box-color);
    border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color);
    border-radius: var(--bs-border-radius-lg);
    margin-bottom: .5rem;
    overflow: hidden;
`;

const Header = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.25rem 0.25rem;
    user-select: none;
    background: var(--ttw-foreground);
    cursor: pointer;

    &:hover {
        background: var(--ttw-box-hover-background);
    }
`;

const HeaderLeft = styled.div`
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.857rem;
    font-weight: 500;
    color: var(--ttw-box-color);
`;

const CollapseButton = styled.button`
    background: none;
    border: none;
    color: var(--bs-secondary);
    padding: 0.375rem;
    border-radius: var(--bs-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
        width: 0.875rem;
        height: 0.875rem;
    }
`;


const Content = styled.div`
    background: var(--ttw-box-background);
    border-top: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color);
`;

const EmptyState = styled.div`
    padding: 1rem 0.75rem;
    text-align: center;
    color: var(--bs-secondary);
    font-size: 0.857rem;
`;

const FileList = styled.div`
    max-height: 12.5rem;
    overflow-y: auto;
`;

const FileItem = styled.div<{ $status: string }>`
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.375rem 0.75rem;
    cursor: pointer;
    font-size: 0.857rem;
    color: var(--ttw-box-color);

    &:hover {
        background: var(--ttw-box-hover-background);
    }

    &:not(:last-child) {
        border-bottom: 1px solid var(--bs-border-color);
    }
`;

const FileContent = styled.div`
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
`;

const FileActions = styled.div`
    display: flex;
    align-items: center;
    opacity: 0;
    transition: opacity 0.2s ease;

    ${FileItem}:hover & {
        opacity: 1;
    }
`;

const DiscardButton = styled(Button)`
    --bs-btn-padding-y: 0.125rem;
    --bs-btn-padding-x: 0.125rem;
    --bs-btn-font-size: 0.75rem;
    width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
        width: 0.75rem;
        height: 0.75rem;
    }
`;

const FileIcon = styled.div<{ $status: string }>`
    margin-right: 0.5rem;
    color: ${props => {
        switch (props.$status) {
            case 'A':
                return 'var(--bs-success)';
            case 'M':
                return 'var(--bs-warning)';
            case 'D':
                return 'var(--bs-danger)';
            default:
                return 'var(--bs-secondary)';
        }
    }};

    svg {
        width: 0.875rem;
        height: 0.875rem;
    }
`;

const FileName = styled.span`
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
`;

const Actions = styled.div`
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0.75rem;
    border-top: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color);
    gap: 0.5rem;
    background: var(--ttw-foreground);
`;
