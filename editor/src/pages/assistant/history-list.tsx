import { request, styled, useOptions } from '@topwrite/common';
import dayjs from 'dayjs';
import { useCallback } from 'react';
import { BsChatDots, BsTrash } from 'react-icons/bs';
import Button from '../../components/button';
import Modal from '../../components/modal';
import ScrollList from '../../components/scroll-list';
import Tooltip from '../../components/tooltip';
import { Conversation, useContext } from './context';

export default function HistoryList() {
    const { assistant } = useOptions();
    const { reset, setWindowState } = useContext();

    const handleSelectConversation = useCallback((conversation: Conversation) => {
        setWindowState('chat');
        reset(conversation);
    }, [setWindowState, reset]);

    return <Container>
        <StyledScrollList
            fetchData={async ({ page = 0 }) => {
                const { data: res } = await request.get<Paginator<Conversation>>(`${assistant}/conversation`, {
                    params: {
                        page: page + 1
                    }
                });

                return {
                    data: res.data,
                    hasMore: res.current_page < res.last_page,
                    page: res.current_page
                };
            }}
            renderList={({ data, setData }) => {
                return data.map((conversation) => {
                    return <ConversationItem key={conversation.id}>
                        <ConversationContent onClick={() => handleSelectConversation(conversation)}>
                            <ConversationIcon>
                                <BsChatDots />
                            </ConversationIcon>
                            <ConversationInfo>
                                <ConversationTitle>
                                    {conversation.title || '未命名对话'}
                                </ConversationTitle>
                                <ConversationDate>
                                    {dayjs(conversation.create_time).fromNow()}
                                </ConversationDate>
                            </ConversationInfo>
                        </ConversationContent>
                        <ConversationActions>
                            <Tooltip tooltip='删除对话'>
                                <DeleteButton
                                    variant='light'
                                    size='sm'
                                    onClick={async () => {
                                        const confirmed = await Modal.confirm({
                                            message: '确定要删除这个对话吗？删除后无法恢复。'
                                        });

                                        if (confirmed) {
                                            await request.delete(`${assistant}/conversation/${conversation.id}`);

                                            setData(draft => {
                                                const index = draft.findIndex(t => t.id === conversation.id);
                                                if (index !== -1) draft.splice(index, 1);
                                            });
                                        }
                                    }}
                                >
                                    <BsTrash />
                                </DeleteButton>
                            </Tooltip>
                        </ConversationActions>
                    </ConversationItem>;
                });
            }}
        />
    </Container>;
}
const StyledScrollList = styled(ScrollList<Conversation>)`
    padding: 0.75rem;
    height: auto;
`;

const Container = styled.div`
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
`;

const ConversationItem = styled.div`
    display: flex;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.5rem;
    background: var(--bs-body-bg);
    transition: all 0.2s ease;
    position: relative;

    &:hover {
        background: var(--bs-secondary-bg);
        border-color: var(--bs-primary);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &:last-child {
        margin-bottom: 0;
    }
`;

const ConversationContent = styled.div`
    display: flex;
    align-items: center;
    flex: 1;
    cursor: pointer;
    min-width: 0; // 防止内容溢出
`;

const ConversationIcon = styled.div`
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    background: var(--ttw-file-active-background);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;

    svg {
        width: 1.25rem;
        height: 1.25rem;
    }
`;

const ConversationInfo = styled.div`
    flex: 1;
    min-width: 0;
`;

const ConversationTitle = styled.div`
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: var(--bs-body-color);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.4;
`;

const ConversationDate = styled.div`
    font-size: 0.8125rem;
    color: var(--bs-secondary);
    line-height: 1.3;
`;

const ConversationActions = styled.div`
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-left: 0.5rem;
    opacity: 0;
    transition: opacity 0.2s ease;

    ${ConversationItem}:hover & {
        opacity: 1;
    }
`;

const DeleteButton = styled(Button)`
    --bs-btn-padding-y: 0.25rem;
    --bs-btn-padding-x: 0.25rem;
    --bs-btn-font-size: 0.875rem;
    width: 1.75rem;
    height: 1.75rem;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
        width: 0.875rem;
        height: 0.875rem;
    }
`;
